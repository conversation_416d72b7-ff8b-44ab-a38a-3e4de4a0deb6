#!/usr/bin/env python3
"""
LED灯光模式配置文件
定义各种预设的LED灯光模式，方便快速切换
"""

# LED颜色常量
class LEDColors:
    """LED颜色常量定义"""
    # 高亮度版本（原始）
    RED_BRIGHT = 0xFF0000      # 红色（高亮）
    GREEN_BRIGHT = 0x00FF00    # 绿色（高亮）
    BLUE_BRIGHT = 0x0000FF     # 蓝色（高亮）
    YELLOW_BRIGHT = 0xFFFF00   # 黄色（高亮）
    PURPLE_BRIGHT = 0xFF00FF   # 紫色（高亮）
    CYAN_BRIGHT = 0x00FFFF     # 青色（高亮）
    WHITE_BRIGHT = 0xFFFFFF    # 白色（高亮）
    ORANGE_BRIGHT = 0xFF8000   # 橙色（高亮）
    PINK_BRIGHT = 0xFF69B4     # 粉色（高亮）

    # 低亮度版本（适合摄像头）
    RED = 0x030000      # 红色（低亮度）
    GREEN = 0x000300    # 绿色（低亮度）
    BLUE = 0x000003     # 蓝色（低亮度）
    YELLOW = 0x030300   # 黄色（低亮度）
    PURPLE = 0x030003   # 紫色（低亮度）
    CYAN = 0x000303     # 青色（低亮度）
    WHITE = 0x030303    # 白色（低亮度）
    ORANGE = 0x030100   # 橙色（低亮度）
    PINK = 0x030103     # 粉色（低亮度）

    # 中等亮度版本
    RED_MID = 0x100000      # 红色（中亮度）
    GREEN_MID = 0x001000    # 绿色（中亮度）
    BLUE_MID = 0x000010     # 蓝色（中亮度）
    YELLOW_MID = 0x101000   # 黄色（中亮度）
    PURPLE_MID = 0x100010   # 紫色（中亮度）
    CYAN_MID = 0x001010     # 青色（中亮度）
    WHITE_MID = 0x101010    # 白色（中亮度）

    # 通用
    BLACK = 0x000000    # 黑色（关闭）

# 预设LED模式
LED_PATTERNS = {
    # 基础闪烁模式
    "red_flash": {
        "name": "红色闪烁",
        "colors": [LEDColors.RED, LEDColors.BLACK],
        "counts": [12, 6],
        "description": "红色闪烁12次，熄灭6次"
    },
    
    "blue_flash": {
        "name": "蓝色闪烁", 
        "colors": [LEDColors.BLUE, LEDColors.BLACK],
        "counts": [10, 5],
        "description": "蓝色闪烁10次，熄灭5次"
    },
    
    "green_flash": {
        "name": "绿色闪烁",
        "colors": [LEDColors.GREEN, LEDColors.BLACK], 
        "counts": [8, 8],
        "description": "绿色闪烁8次，熄灭8次"
    },
    
    # 快速闪烁模式
    "red_fast": {
        "name": "红色快闪",
        "colors": [LEDColors.RED, LEDColors.BLACK],
        "counts": [3, 1],
        "description": "红色快速闪烁"
    },
    
    "blue_fast": {
        "name": "蓝色快闪",
        "colors": [LEDColors.BLUE, LEDColors.BLACK],
        "counts": [3, 1], 
        "description": "蓝色快速闪烁"
    },
    
    # 慢速闪烁模式
    "red_slow": {
        "name": "红色慢闪",
        "colors": [LEDColors.RED, LEDColors.BLACK],
        "counts": [20, 10],
        "description": "红色慢速闪烁"
    },
    
    # 多色循环模式
    "rgb_cycle": {
        "name": "RGB循环",
        "colors": [LEDColors.RED, LEDColors.GREEN, LEDColors.BLUE, LEDColors.BLACK],
        "counts": [6, 6, 6, 6],
        "description": "红绿蓝循环显示"
    },
    
    "rainbow_cycle": {
        "name": "彩虹循环",
        "colors": [LEDColors.RED, LEDColors.ORANGE, LEDColors.YELLOW, 
                  LEDColors.GREEN, LEDColors.CYAN, LEDColors.BLUE, 
                  LEDColors.PURPLE, LEDColors.BLACK],
        "counts": [4, 4, 4, 4, 4, 4, 4, 4],
        "description": "彩虹色循环显示"
    },
    
    # 警示模式
    "warning": {
        "name": "警示模式",
        "colors": [LEDColors.RED, LEDColors.YELLOW],
        "counts": [5, 5],
        "description": "红黄交替警示"
    },
    
    "emergency": {
        "name": "紧急模式", 
        "colors": [LEDColors.RED, LEDColors.BLACK],
        "counts": [2, 2],
        "description": "红色紧急闪烁"
    },
    
    # 状态指示模式
    "ready": {
        "name": "就绪状态",
        "colors": [LEDColors.GREEN, LEDColors.BLACK],
        "counts": [15, 5],
        "description": "绿色表示就绪状态"
    },
    
    "working": {
        "name": "工作状态",
        "colors": [LEDColors.BLUE, LEDColors.BLACK],
        "counts": [8, 4],
        "description": "蓝色表示工作状态"
    },
    
    "error": {
        "name": "错误状态",
        "colors": [LEDColors.RED, LEDColors.BLACK],
        "counts": [1, 1],
        "description": "红色快闪表示错误"
    },
    
    # 特殊效果模式
    "breathing": {
        "name": "呼吸灯效果",
        "colors": [LEDColors.BLUE, LEDColors.BLACK],
        "counts": [30, 30],
        "description": "蓝色呼吸灯效果"
    },
    
    "heartbeat": {
        "name": "心跳效果",
        "colors": [LEDColors.RED, LEDColors.BLACK, LEDColors.RED, LEDColors.BLACK],
        "counts": [2, 2, 2, 10],
        "description": "红色心跳效果"
    },
    
    # 常亮模式
    "red_on": {
        "name": "红色常亮",
        "colors": [LEDColors.RED],
        "counts": [100],
        "description": "红色常亮"
    },
    
    "blue_on": {
        "name": "蓝色常亮", 
        "colors": [LEDColors.BLUE],
        "counts": [100],
        "description": "蓝色常亮"
    },
    
    "green_on": {
        "name": "绿色常亮",
        "colors": [LEDColors.GREEN], 
        "counts": [100],
        "description": "绿色常亮"
    },
    
    # 关闭模式
    "off": {
        "name": "关闭",
        "colors": [LEDColors.BLACK],
        "counts": [100],
        "description": "关闭所有LED"
    },

    # ==================== 摄像头优化模式 ====================
    # 低亮度模式，适合摄像头成像

    "red_camera": {
        "name": "红色闪烁(摄像头)",
        "colors": [LEDColors.RED, LEDColors.BLACK],
        "counts": [12, 6],
        "description": "红色闪烁(低亮度，适合摄像头)"
    },

    "blue_camera": {
        "name": "蓝色闪烁(摄像头)",
        "colors": [LEDColors.BLUE, LEDColors.BLACK],
        "counts": [10, 5],
        "description": "蓝色闪烁(低亮度，适合摄像头)"
    },

    "green_camera": {
        "name": "绿色闪烁(摄像头)",
        "colors": [LEDColors.GREEN, LEDColors.BLACK],
        "counts": [8, 8],
        "description": "绿色闪烁(低亮度，适合摄像头)"
    },

    "rgb_camera": {
        "name": "RGB循环(摄像头)",
        "colors": [LEDColors.RED, LEDColors.GREEN, LEDColors.BLUE, LEDColors.BLACK],
        "counts": [6, 6, 6, 6],
        "description": "RGB循环(低亮度，适合摄像头)"
    },

    "warning_camera": {
        "name": "警示模式(摄像头)",
        "colors": [LEDColors.RED, LEDColors.YELLOW],
        "counts": [5, 5],
        "description": "红黄交替警示(低亮度，适合摄像头)"
    },

    # 中等亮度模式
    "red_mid": {
        "name": "红色闪烁(中亮度)",
        "colors": [LEDColors.RED_MID, LEDColors.BLACK],
        "counts": [12, 6],
        "description": "红色闪烁(中等亮度)"
    },

    "blue_mid": {
        "name": "蓝色闪烁(中亮度)",
        "colors": [LEDColors.BLUE_MID, LEDColors.BLACK],
        "counts": [10, 5],
        "description": "蓝色闪烁(中等亮度)"
    },

    "green_mid": {
        "name": "绿色闪烁(中亮度)",
        "colors": [LEDColors.GREEN_MID, LEDColors.BLACK],
        "counts": [8, 8],
        "description": "绿色闪烁(中等亮度)"
    },

    # 亮度对比测试模式
    "brightness_test": {
        "name": "亮度测试",
        "colors": [LEDColors.RED, LEDColors.RED_MID, LEDColors.RED_BRIGHT, LEDColors.BLACK],
        "counts": [8, 8, 8, 8],
        "description": "红色亮度对比测试(低-中-高-关)"
    }
}

def get_pattern(pattern_name):
    """
    获取指定的LED模式
    
    Args:
        pattern_name: 模式名称
        
    Returns:
        dict: 包含colors和counts的字典，如果模式不存在则返回None
    """
    if pattern_name in LED_PATTERNS:
        pattern = LED_PATTERNS[pattern_name]
        return {
            'colors': pattern['colors'],
            'counts': pattern['counts']
        }
    else:
        print(f"错误: 未找到模式 '{pattern_name}'")
        return None

def list_patterns():
    """列出所有可用的LED模式"""
    print("可用的LED模式:")
    print("=" * 60)
    for name, pattern in LED_PATTERNS.items():
        colors_str = " -> ".join([f"0x{c:06X}" for c in pattern['colors']])
        print(f"{name:15} | {pattern['name']:12} | {pattern['description']}")
        print(f"{'':15} | 颜色序列: {colors_str}")
        print(f"{'':15} | 计数序列: {pattern['counts']}")
        print("-" * 60)

def create_custom_pattern(name, colors, counts, description="自定义模式"):
    """
    创建自定义LED模式
    
    Args:
        name: 模式名称
        colors: 颜色列表
        counts: 计数列表  
        description: 模式描述
    """
    LED_PATTERNS[name] = {
        "name": name,
        "colors": colors,
        "counts": counts,
        "description": description
    }
    print(f"已创建自定义模式: {name}")

if __name__ == "__main__":
    # 显示所有可用模式
    list_patterns()
    
    # 示例：获取特定模式
    print("\n示例用法:")
    pattern = get_pattern("red_flash")
    if pattern:
        print(f"红色闪烁模式: {pattern}")
    
    # 示例：创建自定义模式
    create_custom_pattern(
        name="my_pattern",
        colors=[LEDColors.PURPLE, LEDColors.CYAN, LEDColors.BLACK],
        counts=[5, 5, 3],
        description="紫青交替闪烁"
    )
