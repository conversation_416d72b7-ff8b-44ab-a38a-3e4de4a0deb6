#!/usr/bin/env python3
"""
同步多小车LED控制器
多个小车使用完全相同的灯光模式，同步闪烁
"""

import json
import os
import time
import rospy
from std_msgs.msg import Float32MultiArray
from paho.mqtt import client as mqtt_client

# 设置LED灯的颜色
def LedSetColor(color_list, color):
    color_list.clear()
    color_list.append(((color >> 16) & 0xff) / 256)  # 红色分量
    color_list.append(((color >> 8) & 0xff) / 256)   # 绿色分量
    color_list.append(((color) & 0xff) / 256)        # 蓝色分量
    color_list.append(1)                             # 透明度

class SyncMultiRobotLEDController:
    """同步多小车LED控制器 - 所有小车使用相同的灯光模式"""
    
    def __init__(self, mqtt_client, robot_ids, colors, counts):
        """
        初始化同步多小车LED控制器
        
        Args:
            mqtt_client: MQTT客户端实例
            robot_ids: 小车ID列表，例如 [8, 9, 10]
            colors: 颜色序列，例如 [0xFF0000, 0x000000] (红色闪烁)
            counts: 每种颜色的显示次数，例如 [12, 6]
        """
        self.mqtt_client = mqtt_client
        self.robot_ids = robot_ids
        self.colors = colors
        self.counts = counts
        
        # 当前状态（所有小车共享）
        self.current_color_idx = 0
        self.current_count = 0
        self.color_list = []
        
        print(f"初始化同步LED控制器，控制小车: {self.robot_ids}")
        print(f"灯光模式: {[hex(c) for c in self.colors]}, 计数: {self.counts}")
    
    def update_color(self):
        """更新当前颜色状态（所有小车同步）"""
        # 更新当前颜色的显示次数
        self.current_count += 1
        
        # 如果达到计数上限，切换到下一种颜色
        if self.current_count >= self.counts[self.current_color_idx]:
            self.current_color_idx = (self.current_color_idx + 1) % len(self.colors)
            self.current_count = 0
        
        # 设置当前颜色
        LedSetColor(self.color_list, self.colors[self.current_color_idx])
    
    def publish_all(self):
        """同时发布所有小车的LED状态"""
        current_color = self.colors[self.current_color_idx]
        
        # 为每个小车发布相同的LED状态
        for robot_id in self.robot_ids:
            self._set_ledup(robot_id, current_color)
            self._set_leddown(robot_id, current_color)
    
    def _set_ledup(self, entity_id, led_colors):
        """设置上方LED"""
        json_msg = {
            "cmd_type": "ledup",
            "args_length": 6,
            "args": {
                "0": led_colors,
                "1": 14,
                "2": led_colors,
                "3": 14,
                "4": led_colors,
                "5": 14,
            },
        }
        json_str = json.dumps(json_msg)
        self.mqtt_client.publish(
            f"/VSWARM{entity_id}_robot/cmd", json_str.encode("utf-8")
        )
    
    def _set_leddown(self, entity_id, led_colors):
        """设置下方LED"""
        json_msg = {
            "cmd_type": "leddown",
            "args_length": 6,
            "args": {
                "0": led_colors,
                "1": 30,
                "2": led_colors,
                "3": 30,
                "4": led_colors,
                "5": 30,
            },
        }
        json_str = json.dumps(json_msg)
        self.mqtt_client.publish(
            f"/VSWARM{entity_id}_robot/cmd", json_str.encode("utf-8")
        )
    
    def turn_off_all(self):
        """关闭所有小车的LED"""
        print("关闭所有小车的LED...")
        for robot_id in self.robot_ids:
            self._set_ledup(robot_id, 0x000000)
            self._set_leddown(robot_id, 0x000000)
    
    def get_current_status(self):
        """获取当前状态信息"""
        current_color = self.colors[self.current_color_idx]
        return {
            'current_color': hex(current_color),
            'color_index': self.current_color_idx,
            'count_progress': f"{self.current_count}/{self.counts[self.current_color_idx]}",
            'controlled_robots': self.robot_ids
        }


class MqttClientThread:
    """MQTT客户端线程类"""
    
    def __init__(self, broker, port, keepalive, client_id):
        self.broker = broker
        self.port = port
        self.keepalive = keepalive
        self.reconnect_interval = 1
        self.client_id = client_id
        self.client = self.connect_mqtt()

    def connect_mqtt(self):
        """连接MQTT代理服务器"""
        def on_connect(client, userdata, flags, rc):
            if rc == 0:
                print("Connected to MQTT OK!")
            else:
                print(f"Failed to connect, return code {rc}")
        
        client = mqtt_client.Client(self.client_id)
        client.on_connect = on_connect
        client.connect(self.broker, self.port, self.keepalive)
        return client

    def start_up_mqtt_thread(self):
        """初始化并启动MQTT线程"""
        try:
            broker = os.environ.get("REMOTE_SERVER", self.broker)
            net_status = -1
            while net_status != 0:
                net_status = os.system(f"ping -c 4 {broker}")
                time.sleep(2)
            self.client.loop_start()
        except Exception as e:
            print(f"Error starting MQTT thread: {e}")

    def publish(self, topic, msg):
        """发布消息到指定主题"""
        result = self.client.publish(topic, msg)
        status = result[0]
        if status != 0:
            print(f"Failed to send message to topic {topic}")


def run_sync_led_control(robot_ids, colors, counts, cycles=5, frequency=12):
    """
    运行同步LED控制
    
    Args:
        robot_ids: 小车ID列表
        colors: 颜色序列
        counts: 每种颜色的显示次数
        cycles: 循环周期数
        frequency: 频率(Hz)
    """
    # 初始化ROS节点
    rospy.init_node("sync_multi_robot_led")
    
    # MQTT配置
    broker_ip = "*********"
    port = 1883
    keepalive = 60
    client_id = "SyncMultiRobotLED"
    
    # 创建MQTT客户端
    mqtt_client = MqttClientThread(broker_ip, port, keepalive, client_id)
    mqtt_client.start_up_mqtt_thread()
    
    # 创建同步LED控制器
    led_controller = SyncMultiRobotLEDController(
        mqtt_client=mqtt_client,
        robot_ids=robot_ids,
        colors=colors,
        counts=counts
    )
    
    # 设置循环参数
    rate = rospy.Rate(frequency)
    total_count = cycles * sum(counts)
    current_count = 0
    
    print(f"开始同步LED控制，总计 {total_count} 次更新")
    print("=" * 50)
    
    # 主控制循环
    while not rospy.is_shutdown() and current_count < total_count:
        # 更新LED状态
        led_controller.update_color()
        led_controller.publish_all()
        
        current_count += 1
        
        # 显示状态信息
        status = led_controller.get_current_status()
        robot_list = ", ".join([f"VSWARM{rid}" for rid in robot_ids])
        print(f"进度: {current_count}/{total_count} | "
              f"颜色: {status['current_color']} | "
              f"计数: {status['count_progress']} | "
              f"小车: {robot_list}")
        
        # 等待下一次循环
        rate.sleep()
    
    # 关闭所有LED
    led_controller.turn_off_all()
    print("=" * 50)
    print("同步LED控制完成！")


if __name__ == "__main__":
    # ==================== 配置区域 ====================
    
    # 要控制的小车ID列表（在这里一次性配置所有小车）
    ROBOT_IDS = [8, 9, 10]  # 修改这里来控制不同的小车
    
    # LED灯光模式配置
    LED_COLORS = [0xFF0000, 0x000000]  # 红色闪烁模式
    LED_COUNTS = [12, 6]               # 红色12次，黑色6次
    
    # 运行参数
    CYCLES = 5      # 循环周期数
    FREQUENCY = 12  # 频率(Hz)
    
    # ==================== 其他预设模式 ====================
    
    # 蓝色闪烁模式
    # LED_COLORS = [0x0000FF, 0x000000]
    # LED_COUNTS = [10, 5]
    
    # 绿色闪烁模式
    # LED_COLORS = [0x00FF00, 0x000000]
    # LED_COUNTS = [8, 8]
    
    # 紫色闪烁模式
    # LED_COLORS = [0xFF00FF, 0x000000]
    # LED_COUNTS = [15, 3]
    
    # 三色循环模式
    # LED_COLORS = [0xFF0000, 0x00FF00, 0x0000FF, 0x000000]
    # LED_COUNTS = [6, 6, 6, 6]
    
    # ==================== 运行控制 ====================
    
    print("同步多小车LED控制器")
    print(f"控制小车: {ROBOT_IDS}")
    print(f"灯光模式: {[hex(c) for c in LED_COLORS]}")
    print(f"计数模式: {LED_COUNTS}")
    print(f"循环周期: {CYCLES}")
    print(f"频率: {FREQUENCY}Hz")
    print("-" * 50)
    
    # 运行同步LED控制
    run_sync_led_control(
        robot_ids=ROBOT_IDS,
        colors=LED_COLORS,
        counts=LED_COUNTS,
        cycles=CYCLES,
        frequency=FREQUENCY
    )
