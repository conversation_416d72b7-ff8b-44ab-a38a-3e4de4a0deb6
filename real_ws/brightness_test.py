#!/usr/bin/env python3
"""
LED亮度测试脚本
用于测试不同亮度级别，找到适合摄像头的最佳亮度
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sync_multi_robot_led import run_sync_led_control

def test_brightness_levels(robot_ids, color_base="red"):
    """
    测试不同亮度级别
    
    Args:
        robot_ids: 小车ID列表
        color_base: 基础颜色 ("red", "green", "blue")
    """
    
    # 定义不同亮度级别
    brightness_levels = {
        "red": [
            ("超低亮度 (0x01)", [0x010000, 0x000000]),
            ("低亮度 (0x03)", [0x030000, 0x000000]),
            ("较低亮度 (0x08)", [0x080000, 0x000000]),
            ("中低亮度 (0x10)", [0x100000, 0x000000]),
            ("中等亮度 (0x30)", [0x300000, 0x000000]),
            ("中高亮度 (0x80)", [0x800000, 0x000000]),
            ("高亮度 (0xFF)", [0xFF0000, 0x000000]),
        ],
        "green": [
            ("超低亮度 (0x01)", [0x000100, 0x000000]),
            ("低亮度 (0x03)", [0x000300, 0x000000]),
            ("较低亮度 (0x08)", [0x000800, 0x000000]),
            ("中低亮度 (0x10)", [0x001000, 0x000000]),
            ("中等亮度 (0x30)", [0x003000, 0x000000]),
            ("中高亮度 (0x80)", [0x008000, 0x000000]),
            ("高亮度 (0xFF)", [0x00FF00, 0x000000]),
        ],
        "blue": [
            ("超低亮度 (0x01)", [0x000001, 0x000000]),
            ("低亮度 (0x03)", [0x000003, 0x000000]),
            ("较低亮度 (0x08)", [0x000008, 0x000000]),
            ("中低亮度 (0x10)", [0x000010, 0x000000]),
            ("中等亮度 (0x30)", [0x000030, 0x000000]),
            ("中高亮度 (0x80)", [0x000080, 0x000000]),
            ("高亮度 (0xFF)", [0x0000FF, 0x000000]),
        ]
    }
    
    if color_base not in brightness_levels:
        print(f"错误: 不支持的颜色 '{color_base}'")
        print("支持的颜色: red, green, blue")
        return
    
    levels = brightness_levels[color_base]
    
    print(f"LED亮度测试 - {color_base.upper()}色")
    print("=" * 60)
    print(f"控制小车: {robot_ids}")
    print(f"测试颜色: {color_base}")
    print("每个亮度级别将持续5秒钟")
    print("=" * 60)
    
    try:
        input("按回车键开始测试，或按Ctrl+C取消...")
    except KeyboardInterrupt:
        print("\n已取消")
        return
    
    for i, (level_name, colors) in enumerate(levels):
        print(f"\n[{i+1}/{len(levels)}] 测试 {level_name}")
        print(f"颜色值: {hex(colors[0])}")
        print("持续5秒...")
        
        try:
            run_sync_led_control(
                robot_ids=robot_ids,
                colors=colors,
                counts=[6, 6],  # 快速闪烁便于观察
                cycles=2,       # 2个周期，约5秒
                frequency=12
            )
        except KeyboardInterrupt:
            print("\n用户中断测试")
            break
        
        if i < len(levels) - 1:
            print("等待2秒后进行下一个测试...")
            time.sleep(2)
    
    print("\n亮度测试完成！")

def test_custom_brightness(robot_ids, red_val=0x03, green_val=0x03, blue_val=0x03):
    """
    测试自定义亮度值
    
    Args:
        robot_ids: 小车ID列表
        red_val: 红色分量 (0x00-0xFF)
        green_val: 绿色分量 (0x00-0xFF)  
        blue_val: 蓝色分量 (0x00-0xFF)
    """
    
    custom_color = (red_val << 16) | (green_val << 8) | blue_val
    
    print(f"自定义亮度测试")
    print("=" * 40)
    print(f"控制小车: {robot_ids}")
    print(f"红色分量: 0x{red_val:02X}")
    print(f"绿色分量: 0x{green_val:02X}")
    print(f"蓝色分量: 0x{blue_val:02X}")
    print(f"合成颜色: 0x{custom_color:06X}")
    print("=" * 40)
    
    try:
        input("按回车键开始测试...")
    except KeyboardInterrupt:
        print("\n已取消")
        return
    
    run_sync_led_control(
        robot_ids=robot_ids,
        colors=[custom_color, 0x000000],
        counts=[12, 6],
        cycles=3,
        frequency=12
    )

def compare_brightness(robot_ids):
    """
    对比不同亮度的红色
    """
    print("红色亮度对比测试")
    print("=" * 40)
    print(f"控制小车: {robot_ids}")
    print("将依次显示: 低亮度 -> 中亮度 -> 高亮度 -> 关闭")
    print("每个状态持续3秒")
    print("=" * 40)
    
    try:
        input("按回车键开始对比测试...")
    except KeyboardInterrupt:
        print("\n已取消")
        return
    
    # 对比测试：低-中-高-关
    run_sync_led_control(
        robot_ids=robot_ids,
        colors=[0x030000, 0x300000, 0xFF0000, 0x000000],  # 低-中-高-关
        counts=[18, 18, 18, 18],  # 每个3秒 (18/6Hz = 3秒)
        cycles=2,
        frequency=6  # 降低频率，便于观察
    )

def main():
    """主函数"""
    
    # 默认配置
    default_robot_ids = [8, 9, 10]
    
    if len(sys.argv) < 2:
        print("LED亮度测试工具")
        print("=" * 50)
        print("使用方法:")
        print(f"  python {sys.argv[0]} levels <颜色> [小车ID列表]")
        print(f"  python {sys.argv[0]} custom <R> <G> <B> [小车ID列表]")
        print(f"  python {sys.argv[0]} compare [小车ID列表]")
        print()
        print("示例:")
        print(f"  python {sys.argv[0]} levels red")
        print(f"  python {sys.argv[0]} levels blue 8,9")
        print(f"  python {sys.argv[0]} custom 3 0 0")
        print(f"  python {sys.argv[0]} custom 16 0 0 8,9,10")
        print(f"  python {sys.argv[0]} compare")
        print()
        print("说明:")
        print("  levels  - 测试预设的亮度级别")
        print("  custom  - 测试自定义RGB值 (0-255)")
        print("  compare - 对比低中高亮度")
        print()
        print(f"默认小车ID: {default_robot_ids}")
        return
    
    test_type = sys.argv[1]
    
    if test_type == "levels":
        if len(sys.argv) < 3:
            print("错误: 请指定颜色 (red/green/blue)")
            return
        
        color = sys.argv[2]
        robot_ids = default_robot_ids
        
        if len(sys.argv) > 3:
            try:
                robot_ids = [int(x.strip()) for x in sys.argv[3].split(',')]
            except ValueError:
                print(f"错误: 无效的小车ID列表 '{sys.argv[3]}'")
                return
        
        test_brightness_levels(robot_ids, color)
    
    elif test_type == "custom":
        if len(sys.argv) < 5:
            print("错误: 请指定RGB值")
            print("格式: python brightness_test.py custom <R> <G> <B>")
            return
        
        try:
            red_val = int(sys.argv[2])
            green_val = int(sys.argv[3])
            blue_val = int(sys.argv[4])
            
            if not all(0 <= val <= 255 for val in [red_val, green_val, blue_val]):
                print("错误: RGB值必须在0-255之间")
                return
                
        except ValueError:
            print("错误: RGB值必须是数字")
            return
        
        robot_ids = default_robot_ids
        if len(sys.argv) > 5:
            try:
                robot_ids = [int(x.strip()) for x in sys.argv[5].split(',')]
            except ValueError:
                print(f"错误: 无效的小车ID列表 '{sys.argv[5]}'")
                return
        
        test_custom_brightness(robot_ids, red_val, green_val, blue_val)
    
    elif test_type == "compare":
        robot_ids = default_robot_ids
        if len(sys.argv) > 2:
            try:
                robot_ids = [int(x.strip()) for x in sys.argv[2].split(',')]
            except ValueError:
                print(f"错误: 无效的小车ID列表 '{sys.argv[2]}'")
                return
        
        compare_brightness(robot_ids)
    
    else:
        print(f"错误: 未知的测试类型 '{test_type}'")
        print("支持的类型: levels, custom, compare")

if __name__ == "__main__":
    main()
